<div class="library-container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h5 class="text-primary">
            <i class="bi bi-collection"></i> 已下载的小说
        </h5>
        <button id="updateAllBtn" class="btn btn-primary">
            <i class="bi bi-arrow-clockwise"></i> 更新所有小说
        </button>
    </div>
    
    <div class="row" id="novelList">
        <!-- Downloaded novels will be listed here -->
    </div>
</div>

<script>
// 更新书库列表的渲染
function updateLibraryList(novels) {
    const novelList = document.getElementById('novelList');
    if (!novels || novels.length === 0) {
        novelList.innerHTML = `
            <div class="col">
                <div class="alert alert-info">
                    <i class="bi bi-info-circle"></i> 还没有下载任何小说
                </div>
            </div>`;
        return;
    }

    novelList.innerHTML = novels.map(novel => `
        <div class="col-md-4 mb-3">
            <div class="card shadow-sm h-100">
                <div class="card-body">
                    <h5 class="card-title text-primary">
                        <i class="bi bi-book"></i> ${novel.name}
                    </h5>
                    <div class="card-text">
                        <p class="mb-2">
                            <i class="bi bi-info-circle"></i> 状态: ${novel.status || '未知'}
                        </p>
                        <p class="mb-2">
                            <i class="bi bi-clock"></i> 最后更新: ${novel.last_updated || '未知'}
                        </p>
                    </div>
                    <div class="btn-group">
                        <button class="btn btn-primary btn-sm" 
                                onclick="loadPage('components/reader.html?novel_id=${novel.novel_id}')">
                            <i class="bi bi-book-half"></i> 阅读
                        </button>
                        <button class="btn btn-outline-primary btn-sm" 
                                onclick="downloadNovel('${novel.novel_id}')">
                            <i class="bi bi-arrow-clockwise"></i> 更新
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `).join('');
}

// 加载书库列表
fetch('/api/novels')
    .then(response => response.json())
    .then(novels => updateLibraryList(novels))
    .catch(error => console.error('Error:', error));
</script>