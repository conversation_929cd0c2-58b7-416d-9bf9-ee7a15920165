version: '3.8'

services:
  fanqie:
    build: .
    container_name: fanqie-novel-downloader
    ports:
      - "12930:12930"
    volumes:
      # 持久化用户数据
      - fanqie_data:/app/src/data
      - fanqie_downloads:/app/src/novel_downloads
    restart: unless-stopped
    # 设置资源限制
    deploy:
      resources:
        limits:
          memory: 1G
        reservations:
          memory: 256M

volumes:
  fanqie_data:
    name: fanqie_data
  fanqie_downloads:
    name: fanqie_downloads
