<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>番茄小说下载器</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/style.css') }}" rel="stylesheet">
</head>
<body class="d-flex flex-column min-vh-100">
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark sticky-top">
        <div class="container">
            <a class="navbar-brand" href="#">
                <i class="bi bi-book"></i> 番茄小说下载器
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="#" data-page="search">
                            <i class="bi bi-search"></i> 搜索
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" data-page="library">
                            <i class="bi bi-collection"></i> 书库
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" data-page="reader">
                            <i class="bi bi-book-half"></i> 阅读
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" data-page="settings">
                            <i class="bi bi-gear"></i> 设置
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" id="clearCache">
                            <i class="bi bi-trash"></i> 清理缓存
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="https://github.com/ying-ck/fanqienovel-downloader" target="_blank">
                            <i class="bi bi-github"></i> GitHub
                        </a>
                    </li>
                </ul>
                <div class="d-flex align-items-center">
                    <div class="me-3" id="downloadCount">
                        <span class="badge bg-primary">0</span> 个下载任务
                    </div>
                    <button class="btn btn-outline-light btn-sm" id="showQueueBtn">
                        <i class="bi bi-list-task"></i> 下载队列
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <main class="flex-grow-1">
        <div class="container mt-4">
            <div id="content">
                <!-- Dynamic content will be loaded here -->
            </div>

            <!-- Progress Modal -->
            <div class="modal fade" id="progressModal" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header bg-dark text-white">
                            <h5 class="modal-title">
                                <i class="bi bi-arrow-down-circle"></i> 下载进度
                            </h5>
                            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close" id="minimizeProgress"></button>
                        </div>
                        <div class="modal-body">
                            <div class="progress mb-3">
                                <div class="progress-bar progress-bar-striped progress-bar-animated bg-dark" 
                                     role="progressbar" 
                                     style="width: 0%">0%</div>
                            </div>
                            <div id="currentChapter" class="text-muted"></div>
                            <div class="log-container mt-3">
                                <pre id="logOutput" class="bg-light p-3 rounded"></pre>
                            </div>
                            <div class="mt-3">
                                <h6>下载队列</h6>
                                <div id="queueList" class="list-group">
                                    <!-- Queue items will be listed here -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Floating Progress Button -->
            <button id="showProgress" class="btn btn-dark position-fixed d-none">
                <i class="bi bi-arrow-down-circle"></i>
            </button>

            <!-- Toast Notifications -->
            <div class="position-fixed bottom-0 end-0 p-3" style="z-index: 11">
                <div id="toast-container">
                    <!-- Toasts will be added here -->
                </div>
            </div>
        </div>
    </main>

    <footer class="footer py-3 bg-dark text-light mt-auto">
        <div class="container">
            <p class="mb-0">版本: 1.0.1</p>
            <p class="mb-0">作者: Yck & qxqycb & lingo34</p>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.socket.io/4.0.1/socket.io.min.js"></script>
    <script src="{{ url_for('static', filename='js/main.js') }}"></script>
</body>
</html>
