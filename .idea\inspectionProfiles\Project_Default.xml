<component name="InspectionProjectProfileManager">
  <profile version="1.0">
    <option name="myName" value="Project Default" />
    <inspection_tool class="PyPackageRequirementsInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredPackages">
        <value>
          <list size="370">
            <item index="0" class="java.lang.String" itemvalue="altgraph" />
            <item index="1" class="java.lang.String" itemvalue="google-pasta" />
            <item index="2" class="java.lang.String" itemvalue="numba" />
            <item index="3" class="java.lang.String" itemvalue="jupyterlab_widgets" />
            <item index="4" class="java.lang.String" itemvalue="greenlet" />
            <item index="5" class="java.lang.String" itemvalue="scikit-learn" />
            <item index="6" class="java.lang.String" itemvalue="cufflinks" />
            <item index="7" class="java.lang.String" itemvalue="executing" />
            <item index="8" class="java.lang.String" itemvalue="srsly" />
            <item index="9" class="java.lang.String" itemvalue="torchvision" />
            <item index="10" class="java.lang.String" itemvalue="markdown-it-py" />
            <item index="11" class="java.lang.String" itemvalue="tensorflow_intel" />
            <item index="12" class="java.lang.String" itemvalue="ftfy" />
            <item index="13" class="java.lang.String" itemvalue="patsy" />
            <item index="14" class="java.lang.String" itemvalue="trio" />
            <item index="15" class="java.lang.String" itemvalue="starlette" />
            <item index="16" class="java.lang.String" itemvalue="bleach" />
            <item index="17" class="java.lang.String" itemvalue="jupyter_server_terminals" />
            <item index="18" class="java.lang.String" itemvalue="graphviz" />
            <item index="19" class="java.lang.String" itemvalue="lxml" />
            <item index="20" class="java.lang.String" itemvalue="pyreadline3" />
            <item index="21" class="java.lang.String" itemvalue="soupsieve" />
            <item index="22" class="java.lang.String" itemvalue="et_xmlfile" />
            <item index="23" class="java.lang.String" itemvalue="prometheus_client" />
            <item index="24" class="java.lang.String" itemvalue="PyPDF2" />
            <item index="25" class="java.lang.String" itemvalue="auto-py-to-exe" />
            <item index="26" class="java.lang.String" itemvalue="jsonschema" />
            <item index="27" class="java.lang.String" itemvalue="passlib" />
            <item index="28" class="java.lang.String" itemvalue="xlrd" />
            <item index="29" class="java.lang.String" itemvalue="jax" />
            <item index="30" class="java.lang.String" itemvalue="Werkzeug" />
            <item index="31" class="java.lang.String" itemvalue="tensorboard-data-server" />
            <item index="32" class="java.lang.String" itemvalue="colorlover" />
            <item index="33" class="java.lang.String" itemvalue="wordcloud" />
            <item index="34" class="java.lang.String" itemvalue="click" />
            <item index="35" class="java.lang.String" itemvalue="scikit-surprise" />
            <item index="36" class="java.lang.String" itemvalue="contourpy" />
            <item index="37" class="java.lang.String" itemvalue="pefile" />
            <item index="38" class="java.lang.String" itemvalue="pyinstaller-hooks-contrib" />
            <item index="39" class="java.lang.String" itemvalue="prettytable" />
            <item index="40" class="java.lang.String" itemvalue="openai" />
            <item index="41" class="java.lang.String" itemvalue="pdfminer.six" />
            <item index="42" class="java.lang.String" itemvalue="regex" />
            <item index="43" class="java.lang.String" itemvalue="jupyterlab_pygments" />
            <item index="44" class="java.lang.String" itemvalue="mediapipe" />
            <item index="45" class="java.lang.String" itemvalue="pydantic_core" />
            <item index="46" class="java.lang.String" itemvalue="screeninfo" />
            <item index="47" class="java.lang.String" itemvalue="tensorboard" />
            <item index="48" class="java.lang.String" itemvalue="asttokens" />
            <item index="49" class="java.lang.String" itemvalue="cymem" />
            <item index="50" class="java.lang.String" itemvalue="DownloadKit" />
            <item index="51" class="java.lang.String" itemvalue="matplotlib" />
            <item index="52" class="java.lang.String" itemvalue="murmurhash" />
            <item index="53" class="java.lang.String" itemvalue="narwhals" />
            <item index="54" class="java.lang.String" itemvalue="httpcore" />
            <item index="55" class="java.lang.String" itemvalue="idna" />
            <item index="56" class="java.lang.String" itemvalue="referencing" />
            <item index="57" class="java.lang.String" itemvalue="rsa" />
            <item index="58" class="java.lang.String" itemvalue="wasabi" />
            <item index="59" class="java.lang.String" itemvalue="networkx" />
            <item index="60" class="java.lang.String" itemvalue="confection" />
            <item index="61" class="java.lang.String" itemvalue="json5" />
            <item index="62" class="java.lang.String" itemvalue="trio-websocket" />
            <item index="63" class="java.lang.String" itemvalue="cffi" />
            <item index="64" class="java.lang.String" itemvalue="bottle" />
            <item index="65" class="java.lang.String" itemvalue="mitmproxy-windows" />
            <item index="66" class="java.lang.String" itemvalue="numpy" />
            <item index="67" class="java.lang.String" itemvalue="py4j" />
            <item index="68" class="java.lang.String" itemvalue="jupyter-events" />
            <item index="69" class="java.lang.String" itemvalue="opencv-python-headless" />
            <item index="70" class="java.lang.String" itemvalue="jupyter" />
            <item index="71" class="java.lang.String" itemvalue="selenium" />
            <item index="72" class="java.lang.String" itemvalue="pyOpenSSL" />
            <item index="73" class="java.lang.String" itemvalue="mdurl" />
            <item index="74" class="java.lang.String" itemvalue="smart-open" />
            <item index="75" class="java.lang.String" itemvalue="ddddocr" />
            <item index="76" class="java.lang.String" itemvalue="tldextract" />
            <item index="77" class="java.lang.String" itemvalue="websockets" />
            <item index="78" class="java.lang.String" itemvalue="PyScreeze" />
            <item index="79" class="java.lang.String" itemvalue="annotated-types" />
            <item index="80" class="java.lang.String" itemvalue="Flask-Cors" />
            <item index="81" class="java.lang.String" itemvalue="jsonpointer" />
            <item index="82" class="java.lang.String" itemvalue="Send2Trash" />
            <item index="83" class="java.lang.String" itemvalue="category_encoders" />
            <item index="84" class="java.lang.String" itemvalue="babel" />
            <item index="85" class="java.lang.String" itemvalue="debugpy" />
            <item index="86" class="java.lang.String" itemvalue="statsmodels" />
            <item index="87" class="java.lang.String" itemvalue="argon2-cffi" />
            <item index="88" class="java.lang.String" itemvalue="multidict" />
            <item index="89" class="java.lang.String" itemvalue="thinc" />
            <item index="90" class="java.lang.String" itemvalue="pytz" />
            <item index="91" class="java.lang.String" itemvalue="cloudpathlib" />
            <item index="92" class="java.lang.String" itemvalue="findspark" />
            <item index="93" class="java.lang.String" itemvalue="traitlets" />
            <item index="94" class="java.lang.String" itemvalue="absl-py" />
            <item index="95" class="java.lang.String" itemvalue="protobuf" />
            <item index="96" class="java.lang.String" itemvalue="pywinpty" />
            <item index="97" class="java.lang.String" itemvalue="joblib" />
            <item index="98" class="java.lang.String" itemvalue="jiter" />
            <item index="99" class="java.lang.String" itemvalue="nltk" />
            <item index="100" class="java.lang.String" itemvalue="python-dotenv" />
            <item index="101" class="java.lang.String" itemvalue="h11" />
            <item index="102" class="java.lang.String" itemvalue="cornac" />
            <item index="103" class="java.lang.String" itemvalue="gast" />
            <item index="104" class="java.lang.String" itemvalue="tinycss2" />
            <item index="105" class="java.lang.String" itemvalue="django-extensions" />
            <item index="106" class="java.lang.String" itemvalue="fsspec" />
            <item index="107" class="java.lang.String" itemvalue="pynput" />
            <item index="108" class="java.lang.String" itemvalue="PyQt5-Qt5" />
            <item index="109" class="java.lang.String" itemvalue="python-json-logger" />
            <item index="110" class="java.lang.String" itemvalue="filelock" />
            <item index="111" class="java.lang.String" itemvalue="pyzmq" />
            <item index="112" class="java.lang.String" itemvalue="sentencepiece" />
            <item index="113" class="java.lang.String" itemvalue="keras" />
            <item index="114" class="java.lang.String" itemvalue="pyparsing" />
            <item index="115" class="java.lang.String" itemvalue="beautifulsoup4" />
            <item index="116" class="java.lang.String" itemvalue="memory-profiler" />
            <item index="117" class="java.lang.String" itemvalue="tokenizers" />
            <item index="118" class="java.lang.String" itemvalue="hyperlink" />
            <item index="119" class="java.lang.String" itemvalue="service-identity" />
            <item index="120" class="java.lang.String" itemvalue="uv" />
            <item index="121" class="java.lang.String" itemvalue="isoduration" />
            <item index="122" class="java.lang.String" itemvalue="fqdn" />
            <item index="123" class="java.lang.String" itemvalue="aiohappyeyeballs" />
            <item index="124" class="java.lang.String" itemvalue="cryptography" />
            <item index="125" class="java.lang.String" itemvalue="catalogue" />
            <item index="126" class="java.lang.String" itemvalue="parsel" />
            <item index="127" class="java.lang.String" itemvalue="MouseInfo" />
            <item index="128" class="java.lang.String" itemvalue="urwid" />
            <item index="129" class="java.lang.String" itemvalue="jaxlib" />
            <item index="130" class="java.lang.String" itemvalue="cssselect" />
            <item index="131" class="java.lang.String" itemvalue="widgetsnbextension" />
            <item index="132" class="java.lang.String" itemvalue="argon2-cffi-bindings" />
            <item index="133" class="java.lang.String" itemvalue="itemadapter" />
            <item index="134" class="java.lang.String" itemvalue="bottle-websocket" />
            <item index="135" class="java.lang.String" itemvalue="aioquic" />
            <item index="136" class="java.lang.String" itemvalue="shellingham" />
            <item index="137" class="java.lang.String" itemvalue="locust" />
            <item index="138" class="java.lang.String" itemvalue="distro" />
            <item index="139" class="java.lang.String" itemvalue="matplotlib-inline" />
            <item index="140" class="java.lang.String" itemvalue="webcolors" />
            <item index="141" class="java.lang.String" itemvalue="mypy-extensions" />
            <item index="142" class="java.lang.String" itemvalue="wcwidth" />
            <item index="143" class="java.lang.String" itemvalue="webdriver-manager" />
            <item index="144" class="java.lang.String" itemvalue="h2" />
            <item index="145" class="java.lang.String" itemvalue="llvmlite" />
            <item index="146" class="java.lang.String" itemvalue="spacy-legacy" />
            <item index="147" class="java.lang.String" itemvalue="jupyter_core" />
            <item index="148" class="java.lang.String" itemvalue="w3lib" />
            <item index="149" class="java.lang.String" itemvalue="Jinja2" />
            <item index="150" class="java.lang.String" itemvalue="rfc3986-validator" />
            <item index="151" class="java.lang.String" itemvalue="typeguard" />
            <item index="152" class="java.lang.String" itemvalue="mysql-connector-python" />
            <item index="153" class="java.lang.String" itemvalue="uri-template" />
            <item index="154" class="java.lang.String" itemvalue="sounddevice" />
            <item index="155" class="java.lang.String" itemvalue="pywin32-ctypes" />
            <item index="156" class="java.lang.String" itemvalue="blis" />
            <item index="157" class="java.lang.String" itemvalue="six" />
            <item index="158" class="java.lang.String" itemvalue="typer" />
            <item index="159" class="java.lang.String" itemvalue="prompt_toolkit" />
            <item index="160" class="java.lang.String" itemvalue="parso" />
            <item index="161" class="java.lang.String" itemvalue="ruamel.yaml" />
            <item index="162" class="java.lang.String" itemvalue="ipython" />
            <item index="163" class="java.lang.String" itemvalue="rich" />
            <item index="164" class="java.lang.String" itemvalue="packaging" />
            <item index="165" class="java.lang.String" itemvalue="weasel" />
            <item index="166" class="java.lang.String" itemvalue="fastjsonschema" />
            <item index="167" class="java.lang.String" itemvalue="chardet" />
            <item index="168" class="java.lang.String" itemvalue="jmespath" />
            <item index="169" class="java.lang.String" itemvalue="pandera" />
            <item index="170" class="java.lang.String" itemvalue="schedule" />
            <item index="171" class="java.lang.String" itemvalue="geventhttpclient" />
            <item index="172" class="java.lang.String" itemvalue="aiohttp" />
            <item index="173" class="java.lang.String" itemvalue="async-lru" />
            <item index="174" class="java.lang.String" itemvalue="PyRect" />
            <item index="175" class="java.lang.String" itemvalue="httpx" />
            <item index="176" class="java.lang.String" itemvalue="PySimpleGUI" />
            <item index="177" class="java.lang.String" itemvalue="pytweening" />
            <item index="178" class="java.lang.String" itemvalue="PyYAML" />
            <item index="179" class="java.lang.String" itemvalue="defusedxml" />
            <item index="180" class="java.lang.String" itemvalue="PyMsgBox" />
            <item index="181" class="java.lang.String" itemvalue="pycparser" />
            <item index="182" class="java.lang.String" itemvalue="Pygments" />
            <item index="183" class="java.lang.String" itemvalue="PyQt5" />
            <item index="184" class="java.lang.String" itemvalue="astunparse" />
            <item index="185" class="java.lang.String" itemvalue="imutils" />
            <item index="186" class="java.lang.String" itemvalue="gevent" />
            <item index="187" class="java.lang.String" itemvalue="ttkbootstrap" />
            <item index="188" class="java.lang.String" itemvalue="uvicorn" />
            <item index="189" class="java.lang.String" itemvalue="PyAutoGUI" />
            <item index="190" class="java.lang.String" itemvalue="pywin32" />
            <item index="191" class="java.lang.String" itemvalue="libclang" />
            <item index="192" class="java.lang.String" itemvalue="terminado" />
            <item index="193" class="java.lang.String" itemvalue="comm" />
            <item index="194" class="java.lang.String" itemvalue="pydantic" />
            <item index="195" class="java.lang.String" itemvalue="transformers" />
            <item index="196" class="java.lang.String" itemvalue="pyperclip" />
            <item index="197" class="java.lang.String" itemvalue="DrissionPage" />
            <item index="198" class="java.lang.String" itemvalue="Flask-SQLAlchemy" />
            <item index="199" class="java.lang.String" itemvalue="ipykernel" />
            <item index="200" class="java.lang.String" itemvalue="nbconvert" />
            <item index="201" class="java.lang.String" itemvalue="wsproto" />
            <item index="202" class="java.lang.String" itemvalue="attrs" />
            <item index="203" class="java.lang.String" itemvalue="psutil" />
            <item index="204" class="java.lang.String" itemvalue="simplejson" />
            <item index="205" class="java.lang.String" itemvalue="jedi" />
            <item index="206" class="java.lang.String" itemvalue="flatbuffers" />
            <item index="207" class="java.lang.String" itemvalue="jupyter_server" />
            <item index="208" class="java.lang.String" itemvalue="platformdirs" />
            <item index="209" class="java.lang.String" itemvalue="optree" />
            <item index="210" class="java.lang.String" itemvalue="propcache" />
            <item index="211" class="java.lang.String" itemvalue="PyMySQL" />
            <item index="212" class="java.lang.String" itemvalue="lightgbm" />
            <item index="213" class="java.lang.String" itemvalue="msgpack" />
            <item index="214" class="java.lang.String" itemvalue="PyJWT" />
            <item index="215" class="java.lang.String" itemvalue="decorator" />
            <item index="216" class="java.lang.String" itemvalue="queuelib" />
            <item index="217" class="java.lang.String" itemvalue="itemloaders" />
            <item index="218" class="java.lang.String" itemvalue="mitmproxy" />
            <item index="219" class="java.lang.String" itemvalue="pandocfilters" />
            <item index="220" class="java.lang.String" itemvalue="pyasn1" />
            <item index="221" class="java.lang.String" itemvalue="requests" />
            <item index="222" class="java.lang.String" itemvalue="ldap3" />
            <item index="223" class="java.lang.String" itemvalue="sniffio" />
            <item index="224" class="java.lang.String" itemvalue="websocket-client" />
            <item index="225" class="java.lang.String" itemvalue="gevent-websocket" />
            <item index="226" class="java.lang.String" itemvalue="powerlaw" />
            <item index="227" class="java.lang.String" itemvalue="tensorflow" />
            <item index="228" class="java.lang.String" itemvalue="seaborn" />
            <item index="229" class="java.lang.String" itemvalue="language_data" />
            <item index="230" class="java.lang.String" itemvalue="stack-data" />
            <item index="231" class="java.lang.String" itemvalue="ruamel.yaml.clib" />
            <item index="232" class="java.lang.String" itemvalue="nest-asyncio" />
            <item index="233" class="java.lang.String" itemvalue="opt_einsum" />
            <item index="234" class="java.lang.String" itemvalue="ml-dtypes" />
            <item index="235" class="java.lang.String" itemvalue="djangorestframework" />
            <item index="236" class="java.lang.String" itemvalue="itsdangerous" />
            <item index="237" class="java.lang.String" itemvalue="xgboost" />
            <item index="238" class="java.lang.String" itemvalue="outcome" />
            <item index="239" class="java.lang.String" itemvalue="ipywidgets" />
            <item index="240" class="java.lang.String" itemvalue="blinker" />
            <item index="241" class="java.lang.String" itemvalue="kaitaistruct" />
            <item index="242" class="java.lang.String" itemvalue="scipy" />
            <item index="243" class="java.lang.String" itemvalue="hyperframe" />
            <item index="244" class="java.lang.String" itemvalue="tornado" />
            <item index="245" class="java.lang.String" itemvalue="pyasn1_modules" />
            <item index="246" class="java.lang.String" itemvalue="opencv-python" />
            <item index="247" class="java.lang.String" itemvalue="plotly" />
            <item index="248" class="java.lang.String" itemvalue="publicsuffix2" />
            <item index="249" class="java.lang.String" itemvalue="torch" />
            <item index="250" class="java.lang.String" itemvalue="incremental" />
            <item index="251" class="java.lang.String" itemvalue="overrides" />
            <item index="252" class="java.lang.String" itemvalue="hpack" />
            <item index="253" class="java.lang.String" itemvalue="sortedcontainers" />
            <item index="254" class="java.lang.String" itemvalue="langcodes" />
            <item index="255" class="java.lang.String" itemvalue="mistune" />
            <item index="256" class="java.lang.String" itemvalue="pandas" />
            <item index="257" class="java.lang.String" itemvalue="termcolor" />
            <item index="258" class="java.lang.String" itemvalue="pdfplumber" />
            <item index="259" class="java.lang.String" itemvalue="selenium-stealth" />
            <item index="260" class="java.lang.String" itemvalue="Django" />
            <item index="261" class="java.lang.String" itemvalue="future" />
            <item index="262" class="java.lang.String" itemvalue="mpmath" />
            <item index="263" class="java.lang.String" itemvalue="namex" />
            <item index="264" class="java.lang.String" itemvalue="jupyter-console" />
            <item index="265" class="java.lang.String" itemvalue="typing_extensions" />
            <item index="266" class="java.lang.String" itemvalue="snownlp" />
            <item index="267" class="java.lang.String" itemvalue="yarl" />
            <item index="268" class="java.lang.String" itemvalue="pycryptodome" />
            <item index="269" class="java.lang.String" itemvalue="ConfigArgParse" />
            <item index="270" class="java.lang.String" itemvalue="PyGetWindow" />
            <item index="271" class="java.lang.String" itemvalue="webencodings" />
            <item index="272" class="java.lang.String" itemvalue="Twisted" />
            <item index="273" class="java.lang.String" itemvalue="mysqlclient" />
            <item index="274" class="java.lang.String" itemvalue="notebook_shim" />
            <item index="275" class="java.lang.String" itemvalue="Automat" />
            <item index="276" class="java.lang.String" itemvalue="rfc3339-validator" />
            <item index="277" class="java.lang.String" itemvalue="threadpoolctl" />
            <item index="278" class="java.lang.String" itemvalue="arrow" />
            <item index="279" class="java.lang.String" itemvalue="huggingface-hub" />
            <item index="280" class="java.lang.String" itemvalue="pydivert" />
            <item index="281" class="java.lang.String" itemvalue="python-dateutil" />
            <item index="282" class="java.lang.String" itemvalue="Brotli" />
            <item index="283" class="java.lang.String" itemvalue="nbclient" />
            <item index="284" class="java.lang.String" itemvalue="setuptools" />
            <item index="285" class="java.lang.String" itemvalue="cycler" />
            <item index="286" class="java.lang.String" itemvalue="MarkupSafe" />
            <item index="287" class="java.lang.String" itemvalue="constantly" />
            <item index="288" class="java.lang.String" itemvalue="frozenlist" />
            <item index="289" class="java.lang.String" itemvalue="spacy" />
            <item index="290" class="java.lang.String" itemvalue="pylsqpack" />
            <item index="291" class="java.lang.String" itemvalue="Faker" />
            <item index="292" class="java.lang.String" itemvalue="safetensors" />
            <item index="293" class="java.lang.String" itemvalue="certifi" />
            <item index="294" class="java.lang.String" itemvalue="PyQt5_sip" />
            <item index="295" class="java.lang.String" itemvalue="anyio" />
            <item index="296" class="java.lang.String" itemvalue="PyDispatcher" />
            <item index="297" class="java.lang.String" itemvalue="treys" />
            <item index="298" class="java.lang.String" itemvalue="Markdown" />
            <item index="299" class="java.lang.String" itemvalue="sympy" />
            <item index="300" class="java.lang.String" itemvalue="notebook" />
            <item index="301" class="java.lang.String" itemvalue="opencv-contrib-python" />
            <item index="302" class="java.lang.String" itemvalue="xlwings" />
            <item index="303" class="java.lang.String" itemvalue="dnspython" />
            <item index="304" class="java.lang.String" itemvalue="marisa-trie" />
            <item index="305" class="java.lang.String" itemvalue="Scrapy" />
            <item index="306" class="java.lang.String" itemvalue="jupyter-lsp" />
            <item index="307" class="java.lang.String" itemvalue="jupyter_client" />
            <item index="308" class="java.lang.String" itemvalue="h5py" />
            <item index="309" class="java.lang.String" itemvalue="pure_eval" />
            <item index="310" class="java.lang.String" itemvalue="wrapt" />
            <item index="311" class="java.lang.String" itemvalue="kiwisolver" />
            <item index="312" class="java.lang.String" itemvalue="zope.interface" />
            <item index="313" class="java.lang.String" itemvalue="Eel" />
            <item index="314" class="java.lang.String" itemvalue="jupyterlab_server" />
            <item index="315" class="java.lang.String" itemvalue="coloredlogs" />
            <item index="316" class="java.lang.String" itemvalue="fonttools" />
            <item index="317" class="java.lang.String" itemvalue="PySocks" />
            <item index="318" class="java.lang.String" itemvalue="requests-file" />
            <item index="319" class="java.lang.String" itemvalue="retrying" />
            <item index="320" class="java.lang.String" itemvalue="charset-normalizer" />
            <item index="321" class="java.lang.String" itemvalue="jieba" />
            <item index="322" class="java.lang.String" itemvalue="undetected-chromedriver" />
            <item index="323" class="java.lang.String" itemvalue="spacy-loggers" />
            <item index="324" class="java.lang.String" itemvalue="SQLAlchemy" />
            <item index="325" class="java.lang.String" itemvalue="cloudpickle" />
            <item index="326" class="java.lang.String" itemvalue="mplfinance" />
            <item index="327" class="java.lang.String" itemvalue="pyspark" />
            <item index="328" class="java.lang.String" itemvalue="recommenders" />
            <item index="329" class="java.lang.String" itemvalue="types-python-dateutil" />
            <item index="330" class="java.lang.String" itemvalue="Flask-Login" />
            <item index="331" class="java.lang.String" itemvalue="jsonschema-specifications" />
            <item index="332" class="java.lang.String" itemvalue="preshed" />
            <item index="333" class="java.lang.String" itemvalue="onnxruntime" />
            <item index="334" class="java.lang.String" itemvalue="rpds-py" />
            <item index="335" class="java.lang.String" itemvalue="pypdfium2" />
            <item index="336" class="java.lang.String" itemvalue="pyecharts" />
            <item index="337" class="java.lang.String" itemvalue="urllib3" />
            <item index="338" class="java.lang.String" itemvalue="jupyterlab" />
            <item index="339" class="java.lang.String" itemvalue="zope.event" />
            <item index="340" class="java.lang.String" itemvalue="Flask" />
            <item index="341" class="java.lang.String" itemvalue="pyinstaller" />
            <item index="342" class="java.lang.String" itemvalue="keyboard" />
            <item index="343" class="java.lang.String" itemvalue="python-docx" />
            <item index="344" class="java.lang.String" itemvalue="pymongo" />
            <item index="345" class="java.lang.String" itemvalue="mitmproxy_rs" />
            <item index="346" class="java.lang.String" itemvalue="wheel" />
            <item index="347" class="java.lang.String" itemvalue="hyperopt" />
            <item index="348" class="java.lang.String" itemvalue="nbformat" />
            <item index="349" class="java.lang.String" itemvalue="tzdata" />
            <item index="350" class="java.lang.String" itemvalue="textrank4zh" />
            <item index="351" class="java.lang.String" itemvalue="Protego" />
            <item index="352" class="java.lang.String" itemvalue="DataRecorder" />
            <item index="353" class="java.lang.String" itemvalue="humanfriendly" />
            <item index="354" class="java.lang.String" itemvalue="zstandard" />
            <item index="355" class="java.lang.String" itemvalue="tqdm" />
            <item index="356" class="java.lang.String" itemvalue="fastapi" />
            <item index="357" class="java.lang.String" itemvalue="typing-inspect" />
            <item index="358" class="java.lang.String" itemvalue="colorama" />
            <item index="359" class="java.lang.String" itemvalue="pillow" />
            <item index="360" class="java.lang.String" itemvalue="grpcio" />
            <item index="361" class="java.lang.String" itemvalue="aiosignal" />
            <item index="362" class="java.lang.String" itemvalue="hypothesis" />
            <item index="363" class="java.lang.String" itemvalue="openpyxl" />
            <item index="364" class="java.lang.String" itemvalue="backports.zoneinfo" />
            <item index="365" class="java.lang.String" itemvalue="asgiref" />
            <item index="366" class="java.lang.String" itemvalue="et-xmlfile" />
            <item index="367" class="java.lang.String" itemvalue="sqlparse" />
            <item index="368" class="java.lang.String" itemvalue="scikit-image" />
            <item index="369" class="java.lang.String" itemvalue="dlib" />
          </list>
        </value>
      </option>
    </inspection_tool>
  </profile>
</component>